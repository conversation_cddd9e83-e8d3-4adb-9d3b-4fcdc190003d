"use server";

import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { z } from "zod";
import { RequestService } from "../lib/services/RequestService";
import { RequestStatus, RequestUpdate } from "../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { DocumentUploadService } from "@/app/[lang]/protected/document/lib/services/DocumentUploadService";

// Define the schema for request updates
const updateRequestSchema = z.object({
  title: z.string().min(1, "Title is required").max(255, "Title is too long").optional(),
  description: z.string().min(1, "Description is required").optional(),
  service_type: z.string().min(1, "Service type is required").optional(),
  priority: z.enum(["low", "medium", "high"]).optional(),
  status: z
    .enum(["draft", "pending", "approved", "rejected", "completed", "cancelled", "waitlist"])
    .optional(),
  assignee_id: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});

// Type for the form data
type UpdateRequestFormData = z.infer<typeof updateRequestSchema>;

/**
 * Update an existing request
 * @param id The ID of the request to update
 * @param formData The form data for the request update
 * @returns ActionState with the updated request or error
 */
export const updateRequest = requirePermission(DOMAIN_PERMISSIONS.UPDATE)(async (
  id: string,
  formData: FormData
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Extract and validate form data
    const rawData = Object.fromEntries(formData.entries());
    const validationResult = updateRequestSchema.safeParse(rawData);

    if (!validationResult.success) {
      const errors = validationResult.error.format();
      return {
        success: false,
        error: "Validation failed: " + JSON.stringify(errors),
        data: null,
      };
    }

    const data = validationResult.data;

    // Prepare request update data
    const requestData: RequestUpdate = {
      ...data,
      status: data.status as RequestStatus,
      updated_at: new Date().toISOString(),
    };

    // Update the request
    const response = await RequestService.update(id, requestData);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to update request",
        data: null,
      };
    }

    // Revalidate the requests list page and the specific request page
    revalidatePath("/[lang]/protected/request");
    revalidatePath(`/[lang]/protected/request/[id]`, "page");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error updating request: ${error}`);
    return {
      success: false,
      error: `Unexpected error updating request: ${error}`,
      data: null,
    };
  }
});

/**
 * Update a request and redirect to the request page
 * @param id The ID of the request to update
 * @param formData The form data for the request update
 * @param lang The language code for the redirect URL
 */
export const updateRequestAndRedirect = requirePermission(DOMAIN_PERMISSIONS.UPDATE)(async (
  id: string,
  formData: FormData,
  lang: string = "en"
): Promise<ActionState<{ id: string } | null>> => {
  const result = await updateRequest(id, formData);

  if (result.success && result.data) {
    // Redirect to the request view page
    redirect(`/${lang}/protected/request/${result.data.id}`);
  }

  return result;
});

/**
 * Update the status of a request
 * @param id The ID of the request
 * @param status The new status
 * @returns ActionState with the updated request or error
 */
export const updateRequestStatus = requirePermission(DOMAIN_PERMISSIONS.UPDATE)(async (
  id: string,
  status: RequestStatus
): Promise<ActionState<{ id: string } | null>> => {
  try {
    // Prepare request update data
    const requestData: RequestUpdate = {
      status,
      status_updated_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Update the request
    const response = await RequestService.update(id, requestData);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to update request status",
        data: null,
      };
    }

    // NEW: When status becomes "completed", create case file + document
    if (status === "completed" && response.success) {
      try {
        await createCaseFileWithDocument(id);
      } catch (error) {
        // Log error but don't fail the status update
        logger.error(`Failed to create case file for request ${id}: ${error}`);
      }
    }

    // Revalidate the requests list page and the specific request page
    revalidatePath("/[lang]/protected/request");
    revalidatePath(`/[lang]/protected/request/[id]`, "page");

    return {
      success: true,
      error: "",
      data: { id: response.data.id },
    };
  } catch (error) {
    logger.error(`Unexpected error updating request status: ${error}`);
    return {
      success: false,
      error: `Unexpected error updating request status: ${error}`,
      data: null,
    };
  }
});

/**
 * Create case file with document when request is completed
 * Ultra simple MVP implementation
 */
async function createCaseFileWithDocument(requestId: string) {
  const userProfile = await auth.getCurrentUserProfile();
  if (!userProfile?.organizationId) {
    logger.error("No organization context for case file creation");
    return;
  }

  try {
    // 1. Get request details with contacts
    const supabase = await createClient();
    const { data: request, error: requestError } = await supabase
      .from("requests")
      .select(
        `
        *,
        services(*),
        request_contacts(
          id,
          relationship_type,
          contacts(*)
        )
      `
      )
      .eq("id", requestId)
      .single();

    if (requestError || !request) {
      logger.error(`Failed to get request details: ${requestError?.message}`);
      return;
    }

    // Find primary contact (first contact or one with specific relationship)
    const primaryContactRelation =
      request.request_contacts?.find(
        (rc: any) => rc.relationship_type === "primary" || rc.relationship_type === "family"
      ) || request.request_contacts?.[0];

    const primaryContact = primaryContactRelation?.contacts;

    // 2. Create case file
    const { data: caseFile, error: caseFileError } = await supabase
      .from("case_files")
      .insert({
        organization_id: userProfile.organizationId,
        request_id: requestId,
        case_number: `CF-${Date.now()}`, // Simple case number
        status: "opening",
        created_by: userProfile.id,
        metadata: {
          auto_generated: true,
          generated_from_request: requestId,
          generated_at: new Date().toISOString(),
          primary_contact_id: primaryContact?.id || null,
          service_id: request.service_id,
        },
      })
      .select()
      .single();

    if (caseFileError || !caseFile) {
      logger.error(`Failed to create case file: ${caseFileError?.message}`);
      return;
    }

    logger.info(`Case file ${caseFile.case_number} created for request ${requestId}`);

    // 3. Copy contacts from request to case file
    const caseFileContacts = await copyContactsToCaseFile(
      caseFile.id,
      request.request_contacts,
      userProfile.organizationId
    );

    // 4. Generate simple service agreement document
    await generateServiceAgreement(caseFile, request, caseFileContacts);

    // Revalidate case files page
    revalidatePath("/[lang]/protected/case-file");
  } catch (error) {
    logger.error(`Error in createCaseFileWithDocument: ${error}`);
  }
}

/**
 * Copy contacts from request to case file
 */
async function copyContactsToCaseFile(
  caseFileId: string,
  requestContacts: any[],
  organizationId: string
) {
  try {
    const supabase = await createClient();

    if (!requestContacts || requestContacts.length === 0) {
      logger.info("No contacts to copy to case file");
      return [];
    }

    // Create case_file_contacts entries
    const caseFileContactsData = requestContacts.map((rc: any) => ({
      organization_id: organizationId,
      case_file_id: caseFileId,
      contact_id: rc.contact_id,
      relationship_type: rc.relationship_type,
    }));

    const { data: caseFileContacts, error: contactsError } = await supabase
      .from("case_file_contacts")
      .insert(caseFileContactsData)
      .select("*");

    if (contactsError) {
      logger.error(`Failed to copy contacts to case file: ${contactsError.message}`);
      return [];
    }

    logger.info(`Copied ${caseFileContacts?.length || 0} contacts to case file ${caseFileId}`);
    return caseFileContacts || [];
  } catch (error) {
    logger.error(`Error in copyContactsToCaseFile: ${error}`);
    return [];
  }
}

/**
 * Generate a simple service agreement document using the upload API
 */
async function generateServiceAgreement(caseFile: any, request: any, caseFileContacts: any[]) {
  try {
    const supabase = await createClient();

    // Find any template with "service" or "agreement" in the name
    const { data: template, error: templateError } = await supabase
      .from("document_templates")
      .select("*")
      .eq("organization_id", caseFile.organization_id)
      .eq("is_active", true)
      .or("name.ilike.%service%,name.ilike.%agreement%")
      .limit(1)
      .single();

    if (templateError || !template) {
      logger.info("No service template found, skipping document generation");
      return;
    }

    // Find primary case file contact
    const primaryCaseFileContact =
      caseFileContacts.find(
        (cfc: any) => cfc.relationship_type === "primary" || cfc.relationship_type === "family"
      ) || caseFileContacts[0];

    if (!primaryCaseFileContact) {
      logger.info("No case file contacts found, skipping document generation");
      return;
    }

    // Simple token replacement
    let content = template.content;
    const contactName = primaryCaseFileContact.contacts?.name || "Contact Name";
    const currentDate = new Date().toLocaleDateString();
    const orgName = "Your Organization"; // Could be fetched from organization table

    content = content.replace(/\{\{contact\.name\}\}/g, contactName);
    content = content.replace(/\{\{case\.case_number\}\}/g, caseFile.case_number);
    content = content.replace(/\{\{date\.current\}\}/g, currentDate);
    content = content.replace(/\{\{organization\.name\}\}/g, orgName);

    // Create a File object from the generated content
    const fileName = `${caseFile.case_number}-service-agreement-${Date.now()}.html`;
    const file = DocumentUploadService.createFileFromContent(content, fileName, "text/html");

    // Prepare metadata for the generated document
    const additionalMetadata = {
      template_id: template.id,
      template_name: template.name,
      generated_at: new Date().toISOString(),
      auto_generated: true,
      source_request_id: request.id,
      case_file_contact_id: primaryCaseFileContact.id,
      contact_name: contactName,
      relationship_type: primaryCaseFileContact.relationship_type,
    };

    // Use the DocumentUploadService to store the document
    const uploadResult = await DocumentUploadService.uploadDocument({
      file,
      documentName: `Service Agreement - ${contactName} - ${caseFile.case_number}`,
      attachedToType: "case_file",
      attachedToId: caseFile.id,
      category: "service_agreement",
      description: "Auto-generated service agreement document",
      tags: ["auto-generated", "service-agreement", template.name],
      templateId: template.id,
      contactId: primaryCaseFileContact.contact_id,
      attachmentType: "generated",
      additionalMetadata,
    });

    if (!uploadResult.success) {
      logger.error(`Failed to upload generated document: ${uploadResult.message}`);
      return;
    }

    logger.info(`Service agreement generated and uploaded for case file ${caseFile.case_number}`);
    logger.info(`Document ID: ${uploadResult.data?.id}`);
  } catch (error) {
    logger.error(`Error in generateServiceAgreement: ${error}`);
  }
}
