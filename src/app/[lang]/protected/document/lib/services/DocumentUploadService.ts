import { createClient } from "@/lib/supabase/server";
import { logger } from "@/lib/logger/services/LoggerService";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";
import { FileValidationService } from "./FileValidationService";
import { auth } from "@/lib/authentication/services/AuthenticationService";
import { Database } from "@/lib/types/database.types";
import {
  ServiceResponse,
  successResponse,
  errorResponse,
} from "@/lib/types/responses/serviceResponse";

type DocumentAttachmentInsert = Database["public"]["Tables"]["document_attachments"]["Insert"];

export interface UploadDocumentParams {
  file: File;
  documentName: string;
  attachedToType: string;
  attachedToId: string;
  category?: string;
  tags?: string[];
  description?: string;
  templateId?: string;
  contactId?: string;
  attachmentType?: "manual" | "generated";
  additionalMetadata?: Record<string, any>;
}

export interface UploadResult {
  id: string;
  document_name: string;
  file_path: string;
  document_type: string;
  file_size: number;
  metadata: any;
}

/**
 * Internal service for uploading documents to storage and database
 * Can be used by both API routes and server actions
 */
export class DocumentUploadService {
  /**
   * Upload a document file to storage and create database record
   */
  static async uploadDocument(
    params: UploadDocumentParams
  ): Promise<ServiceResponse<UploadResult>> {
    try {
      // Get current user and organization context
      const user = await auth.getCurrentUser();
      if (!user) {
        return errorResponse(null, "Authentication required");
      }

      const organization = await ProfileService.getCurrentOrganization();
      if (!organization) {
        return errorResponse(null, "Organization not found");
      }

      const {
        file,
        documentName,
        attachedToType,
        attachedToId,
        category,
        tags = [],
        description,
        templateId,
        contactId,
        attachmentType = "manual",
        additionalMetadata = {},
      } = params;

      // Validate required fields
      if (!documentName || !documentName.trim()) {
        return errorResponse(null, "Document name is required");
      }

      if (!attachedToType || !attachedToId) {
        return errorResponse(null, "attached_to_type and attached_to_id are required");
      }

      // Validate file
      const validation = FileValidationService.validateFile(file);
      if (!validation.success) {
        return errorResponse(null, validation.error || "File validation failed");
      }

      // Generate unique file path
      const fileExtension = file.name.split(".").pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExtension}`;
      const filePath = `${organization.id}/${attachedToType}/${attachedToId}/${fileName}`;

      // Upload file to Supabase Storage
      const supabase = await createClient();
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from("documents")
        .upload(filePath, file, {
          cacheControl: "3600",
          upsert: false,
        });

      if (uploadError) {
        logger.error(`Error uploading file: ${uploadError.message}`);
        return errorResponse(uploadError, `Upload failed: ${uploadError.message}`);
      }

      // Create attachment record
      const attachmentData: DocumentAttachmentInsert = {
        organization_id: organization.id,
        attached_to_type: attachedToType,
        attached_to_id: attachedToId,
        document_name: documentName.trim(),
        file_path: uploadData.path,
        document_type: fileExtension?.toUpperCase() || "UNKNOWN",
        file_size: file.size,
        attachment_type: attachmentType,
        status: "attached",
        uploaded_by: user.id,
        template_id: templateId || null,
        contact_id: contactId || null,
        metadata: {
          category: category || null,
          tags: tags || [],
          description: description || null,
          mime_type: file.type,
          original_name: file.name,
          ...additionalMetadata,
        },
      };

      const { data: attachment, error: dbError } = await supabase
        .from("document_attachments")
        .insert(attachmentData)
        .select()
        .single();

      if (dbError) {
        // Clean up uploaded file if database insert fails
        await supabase.storage.from("documents").remove([uploadData.path]);
        logger.error(`Error creating attachment record: ${dbError.message}`);
        return errorResponse(dbError, `Database error: ${dbError.message}`);
      }

      logger.info(`Document uploaded successfully: ${documentName}`);

      return successResponse({
        id: attachment.id,
        document_name: attachment.document_name,
        file_path: attachment.file_path,
        document_type: attachment.document_type,
        file_size: attachment.file_size || 0,
        metadata: attachment.metadata,
      });
    } catch (error) {
      logger.error("Error in DocumentUploadService.uploadDocument:", error as Error);
      return errorResponse(error, "Internal server error");
    }
  }

  /**
   * Create a File object from content for generated documents
   */
  static createFileFromContent(
    content: string,
    fileName: string,
    mimeType: string = "text/html"
  ): File {
    const blob = new Blob([content], { type: mimeType });
    return new File([blob], fileName, { type: mimeType });
  }
}
